Based on the Composio codebase, here's how you get and store the connected account ID for an MCP server:

## Getting the Connected Account ID

When you initiate a connection using the `initiate` method, it immediately returns a `ConnectionRequest` object that contains the `connectedAccountId` - you don't have to wait for authentication to complete to get this ID. [1](#0-0)

The `ConnectionRequest` class stores the `connectedAccountId` as a property: [2](#0-1)

## Storing the Connection ID

You should store the `connectedAccountId` in your database immediately after calling the `initiate` method, before redirecting the user for authentication. This ID is generated when the connection is initiated and remains constant throughout the authentication process.

## Waiting for Authentication Completion

After the user completes authentication through the redirect URL, use the `waitUntilActive` method to wait for the connection to become active: [3](#0-2)

For Python, the equivalent method is `wait_until_active`: [4](#0-3)

## Complete Flow Summary

1. Call `initiate` method with your integration details
2. **Immediately store** the returned `connectedAccountId` in your database
3. Redirect user to the `redirectUrl` for authentication
4. Use `waitUntilActive` to poll until the connection status becomes "ACTIVE"
5. The connection is now ready to use with the stored `connectedAccountId`

## Notes

The connected account ID is available immediately upon connection initiation, not after authentication completion. This allows you to store the mapping between your user and the connection ID before the OAuth flow completes. The `waitUntilActive` method handles the polling to determine when the user has successfully completed authentication and the connection is ready for use.
