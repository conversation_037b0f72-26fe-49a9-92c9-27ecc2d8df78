import { createQuery<PERSON>eys } from "@/hooks/use-query";

export const agent<PERSON>eys = createQueryKeys({
  all: ['agents'] as const,
  lists: () => [...agentKeys.all, 'list'] as const,
  list: (filters?: Record<string, any>) => [...agentKeys.lists(), filters] as const,
  details: () => [...agentKeys.all, 'detail'] as const,
  detail: (id: string) => [...agentKeys.details(), id] as const,
  threadAgents: () => [...agentKeys.all, 'thread-agent'] as const,
  threadAgent: (threadId: string) => [...agentKeys.threadAgents(), threadId] as const,
  builderChatHistory: (agentId: string) => [...agentKeys.all, 'builderChatHistory', agentId] as const,
  defaultMCPs: () => [...agentKeys.all, 'default-mcps'] as const,
});
