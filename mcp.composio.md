# Composio MCP Server Connection ID Research

## Overview

Research on how to get connection IDs when instantiating MCP servers for users in Composio, specifically focusing on appending user IDs to MCP server instances created from integrations in the Composio dashboard.

## Key Findings

### 1. MCP Server URL Structure

When you create an MCP server through Composio, you get a URL like:

```
https://mcp.composio.dev/composio/server/<SERVER_ID>?transport=sse
```

### 2. Critical Query Parameters for User Binding

| Query Parameter                        | Purpose                                                                               |
| -------------------------------------- | ------------------------------------------------------------------------------------- |
| `user_id`                              | Bind the session to a user identifier from your app                                   |
| `connected_account_id`                 | Pin the session to a specific Composio `connectedAccount` (skip account selection)    |
| `include_composio_helper_actions=true` | Inject helper tools so the agent can walk the user through authentication when needed |

### 3. Getting Connection IDs - Complete Flow

#### Step 1: Retrieve User's Connected Accounts

```python
from composio import ComposioToolSet, App

toolset = ComposioToolSet(api_key="your_api_key")
connections = toolset.get_connected_accounts()

# Example output shows multiple connections:
# - One ACTIVE connection: 88b5f9fc-579f-40cd-942a-6430c4e0db74
# - Three INITIALIZING connections (incomplete auth)
```

#### Step 2: Filter for Active Connections

```python
def get_user_connection_id(user_id, app_name="gmail"):
    toolset = ComposioToolSet(api_key="your_api_key")
    connections = toolset.get_connected_accounts(entity_id=user_id)

    # Find active connection for the specific app
    for conn in connections:
        if conn.appName == app_name and conn.status == "ACTIVE":
            return conn.id
    return None
```

#### Step 3: Generate User-Specific MCP URL

```python
def generate_user_mcp_url(server_id, user_id, connection_id):
    base_url = f"https://mcp.composio.dev/composio/server/{server_id}"
    return f"{base_url}?user_id={user_id}&connected_account_id={connection_id}"

# Example result:
# https://mcp.composio.dev/composio/server/5bc757cc-7a8e-431c-8616-7f57cbed2423?user_id=user_123&connected_account_id=88b5f9fc-579f-40cd-942a-6430c4e0db74
```

### 4. Creating MCP Servers via API

```bash
curl -X POST https://backend.composio.dev/api/v3/mcp/servers \
  -H "x-api-key: <YOUR_API_KEY>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Gmail",
    "apps": ["gmail"],
    "auth_config_id": {}
  }'
```

**Response includes:**

- `id`: Server ID for URL construction
- `mcp_url`: Base URL for connections
- `commands`: Setup commands for different clients (Cursor, Claude, Windsurf)

### 5. Entity ID Concept (Important!)

- **What**: `entity_id` is a unique ID representing your end-user within Composio
- **Purpose**: Maps to your app's user database/UUID for multi-tenant scenarios
- **Default**: Composio uses `"default"` (only suitable for single-user/testing)
- **Usage**: Pass when getting Entity object: `entity = toolset.get_entity(id=user_identifier_from_my_app)`

### 6. Authentication Flow Options

#### Option 1: Pre-authenticate Users (Recommended)

- Use Composio SDK/API to connect users upfront
- Better for production environments
- More control over auth flow

#### Option 2: On-demand Authentication

- Add `include_composio_helper_actions=true` to URL
- Agent guides user through auth when needed
- Good for development/testing

### 7. Complete Implementation Example

```python
class ComposioMCPManager:
    def __init__(self, api_key):
        self.toolset = ComposioToolSet(api_key=api_key)

    def get_user_mcp_url(self, server_id, user_id, app_name="gmail"):
        # Get user's active connection for the app
        connections = self.toolset.get_connected_accounts(entity_id=user_id)

        active_connection = None
        for conn in connections:
            if conn.appName == app_name and conn.status == "ACTIVE":
                active_connection = conn.id
                break

        if not active_connection:
            raise Exception(f"No active {app_name} connection found for user {user_id}")

        # Generate user-specific MCP URL
        base_url = f"https://mcp.composio.dev/composio/server/{server_id}"
        return f"{base_url}?user_id={user_id}&connected_account_id={active_connection}"

    def create_mcp_server(self, name, apps, auth_config_id=None):
        # Implementation would call the API to create server
        # Returns server_id for URL generation
        pass
```

### 8. Key Benefits of This Approach

1. **Multi-tenant Support**: Each user gets isolated authentication
2. **Scalability**: One MCP server serves multiple users
3. **Security**: User-specific credentials maintained
4. **Flexibility**: Dynamic binding of users to servers
5. **Standardization**: Uses MCP protocol for consistent integration

### 9. Integration with Atlas

For Atlas implementation:

- Create MCP servers for each integrated app (Gmail, Discord, etc.)
- Store server IDs in your database
- Generate user-specific URLs when users access tools
- Use Atlas user IDs as `entity_id` in Composio

### 10. Important Notes

- **SSE Deprecation**: `/sse` transport will be deprecated, use `/mcp` endpoint
- **Connection Status**: Only use `ACTIVE` connections, ignore `INITIALIZING` ones
- **Error Handling**: Always check for active connections before generating URLs
- **Testing**: Use MCP Inspector for local testing before production deployment

## Next Steps

1. Test connection ID retrieval with your current Composio setup
2. Create a test MCP server via API
3. Implement user-specific URL generation
4. Integrate with Atlas user management system
5. Test with multiple users to verify isolation

## Flow Diagram

```mermaid
flowchart TD
    A[User Requests Tool Access] --> B{MCP Server Exists?}

    B -->|No| C[Create MCP Server via API]
    C --> D[Store Server ID in Database]
    D --> E[Get User's Entity ID]

    B -->|Yes| E[Get User's Entity ID]

    E --> F[Query User's Connected Accounts]
    F --> G{Active Connection Exists?}

    G -->|No| H[Initiate User Authentication]
    H --> I[User Completes OAuth Flow]
    I --> J[Connection Status: ACTIVE]
    J --> K[Get Connection ID]

    G -->|Yes| K[Get Connection ID]

    K --> L[Generate User-Specific MCP URL]
    L --> M[Append Query Parameters]
    M --> N[Final MCP URL with user_id & connected_account_id]

    N --> O[Client Connects to MCP Server]
    O --> P[Server Validates User Credentials]
    P --> Q[Tools Available for User]

    subgraph "MCP URL Structure"
        R["Base: https://mcp.composio.dev/composio/server/{server_id}"]
        S["+ ?user_id={atlas_user_id}"]
        T["+ &connected_account_id={composio_connection_id}"]
        U["+ &include_composio_helper_actions=true (optional)"]
    end

    subgraph "Database Storage"
        V[Atlas User ID]
        W[MCP Server ID]
        X[App Integration Config]
    end

    subgraph "Composio Side"
        Y[Entity ID = Atlas User ID]
        Z[Connected Account ID]
        AA[OAuth Tokens/Credentials]
    end

    style A fill:#e1f5fe
    style Q fill:#c8e6c9
    style H fill:#fff3e0
    style C fill:#f3e5f5
```

## Detailed Flow Explanation

### Phase 1: Setup & Initialization

1. **User Request**: Atlas user requests access to external tool (Gmail, Discord, etc.)
2. **Server Check**: System checks if MCP server exists for the requested app
3. **Server Creation**: If not exists, create via Composio API and store server ID

### Phase 2: User Authentication & Connection

4. **Entity Mapping**: Map Atlas user ID to Composio entity ID
5. **Connection Query**: Check user's existing connections for the app
6. **Auth Flow**: If no active connection, initiate OAuth flow
7. **Connection ID**: Retrieve active connection ID from Composio

### Phase 3: URL Generation & Access

8. **URL Construction**: Build user-specific MCP URL with query parameters
9. **Client Connection**: MCP client connects using the generated URL
10. **Tool Access**: User gets authenticated access to external app tools

### Key Decision Points

- **Server Existence**: Determines if new server creation is needed
- **Active Connection**: Determines if user needs to authenticate
- **Connection Status**: Only ACTIVE connections are used for tool access

## Resources

- [Composio MCP Documentation](https://docs.composio.dev/mcp/introduction)
- [MCP API Reference](https://docs.composio.dev/api-reference/api-reference/v3/mcp/get-mcp-servers)
- [User Connection Guide](https://docs.composio.dev/auth/connection)
