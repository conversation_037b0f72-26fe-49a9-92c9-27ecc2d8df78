URL Generation: Composio API generates user-specific MCP URLs

- url generation part will be completely removed

NEW STEP TO THIS PROCESS:

- we want to create a constants file which will contain all the mcp server urls for each app. This file will define what all composio/custom mcp servers are available to the users - the mcp urls stored in the constants file will be in this format : https://mcp.composio.dev/composio/server/3adcafb4-4a7d-4b97-bb26-3be782883e06/mcp (gmail)

- this file will be the central source the custom mcp sever definatinos for our app and what servers uses can use and have access to
- each time a user tries to connect to one of the apps (for example gmail), we will pull that user's user id from supabase and use that to create the users mcp url.
- we append this url as well as include_composio_helper_actions=true to the end of the url like so:
  - https://mcp.composio.dev/composio/server/3adcafb4-4a7d-4b97-bb26-3be782883e06/mcp?user_id=<uuid>&include_composio_helper_actions=true
- this new mcp url (the users personal mcp url) will be passed to the next step (storage) in the same exact manner - wrong

Storage: URLs stored as HTTP custom MCPs in agents.custom_mcps JSONB column

- this will remain the same

Discovery: Tools discovered via HTTP MCP client connection

- this will remain the same

Execution: Tools executed through existing MCP tool wrapper infrastructure

- this will remain the same

Authentication: OAuth flow initiated via special {APP}\_INITIATE_CONNECTION tools

- we want to see if we can make the initiate connection endpoint faster possibly by not going through the mcp wrapper somehow if thats possible
