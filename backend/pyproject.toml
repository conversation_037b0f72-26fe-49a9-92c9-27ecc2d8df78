[tool.poetry]
name = "Atlas"
version = "1.0"
description = "open source generalist AI Agent"
authors = ["mark<PERSON>-<PERSON>rae<PERSON> <<EMAIL>>"]
readme = "README.md"
license = "MIT"
homepage = "https://www.atlasagents.ai/"
repository = "https://github.com/Atlas-ai/Atlas"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

[tool.poetry.dependencies]
python = "^3.11"
python-dotenv = "1.0.1"
litellm = "1.66.1"
click = "8.1.7"
questionary = "2.0.1"
requests = "^2.31.0"
packaging = "24.1"
setuptools = "75.3.0"
pytest = "8.3.3"
pytest-asyncio = "0.24.0"
asyncio = "3.4.3"
altair = "4.2.2"
prisma = "0.15.0"
fastapi = "0.110.0"
uvicorn = "0.27.1"
python-multipart = "0.0.20"
redis = "5.2.1"
upstash-redis = "1.3.0"
supabase = "^2.15.0"
pyjwt = "2.10.1"
exa-py = "^1.9.1"
e2b-code-interpreter = "^1.2.0"
certifi = "2024.2.2"
python-ripgrep = "0.0.6"
daytona_sdk = "^0.20.2"
boto3 = "^1.34.0"
openai = "^1.72.0"
nest-asyncio = "^1.6.0"
vncdotool = "^1.2.0"
tavily-python = "^0.5.4"
pytesseract = "^0.3.13"
stripe = "^12.0.1"
dramatiq = "^1.17.1"
pika = "^1.3.2"
prometheus-client = "^0.21.1"
langfuse = "^2.60.5"
Pillow = "^10.0.0"
mcp = "^1.0.0"
sentry-sdk = {extras = ["fastapi"], version = "^2.29.1"}
httpx = "^0.28.0"
aiohttp = "^3.9.0"
email-validator = "^2.0.0"
mailtrap = "^2.0.1"

[tool.poetry.scripts]
agentpress = "agentpress.cli:main"

[[tool.poetry.packages]]
include = "agentpress"

[tool.poetry.group.dev.dependencies]
daytona-sdk = "^0.20.2"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
