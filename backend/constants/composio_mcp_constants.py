"""
Composio MCP Constants and Utilities

This module provides constants and utilities for working with Composio MCP servers.
It loads the server URLs from the JSON constants file and provides helper functions
for URL generation and validation.
"""

import json
import os
from typing import Dict, Optional, List
from utils.logger import logger

# Path to the constants file
CONSTANTS_FILE_PATH = os.path.join(os.path.dirname(__file__), "composio_mcp_servers.json")

class ComposioMCPConstants:
    """Utility class for managing Composio MCP server constants."""
    
    _server_urls: Optional[Dict[str, str]] = None
    
    @classmethod
    def _load_constants(cls) -> Dict[str, str]:
        """Load MCP server URLs from the constants file."""
        if cls._server_urls is not None:
            return cls._server_urls
            
        try:
            with open(CONSTANTS_FILE_PATH, 'r') as f:
                cls._server_urls = json.load(f)
            logger.info(f"Loaded {len(cls._server_urls)} Composio MCP server URLs from constants file")
            return cls._server_urls
        except FileNotFoundError:
            logger.error(f"Composio MCP constants file not found at {CONSTANTS_FILE_PATH}")
            cls._server_urls = {}
            return cls._server_urls
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing Composio MCP constants file: {e}")
            cls._server_urls = {}
            return cls._server_urls
        except Exception as e:
            logger.error(f"Unexpected error loading Composio MCP constants: {e}")
            cls._server_urls = {}
            return cls._server_urls
    
    @classmethod
    def get_base_url(cls, app_key: str) -> Optional[str]:
        """
        Get the base MCP URL for a given app key.
        
        Args:
            app_key: The app key (e.g., "gmail", "slack", "github")
            
        Returns:
            The base MCP URL or None if not found
        """
        server_urls = cls._load_constants()
        return server_urls.get(app_key)
    
    @classmethod
    def generate_user_mcp_url(cls, app_key: str, user_id: str) -> Optional[str]:
        """
        Generate a user-specific MCP URL by appending user_id and helper actions parameter.
        
        Args:
            app_key: The app key (e.g., "gmail", "slack", "github")
            user_id: The user's UUID
            
        Returns:
            The complete user-specific MCP URL or None if app_key not found
        """
        base_url = cls.get_base_url(app_key)
        if not base_url:
            logger.warning(f"No base URL found for app key: {app_key}")
            return None
            
        # Append user_id and include_composio_helper_actions=true
        user_url = f"{base_url}?user_id={user_id}&include_composio_helper_actions=true"
        logger.info(f"Generated user MCP URL for {app_key}: {user_url}")
        return user_url
    
    @classmethod
    def get_supported_apps(cls) -> List[str]:
        """
        Get list of all supported app keys.
        
        Returns:
            List of supported app keys
        """
        server_urls = cls._load_constants()
        return list(server_urls.keys())
    
    @classmethod
    def is_app_supported(cls, app_key: str) -> bool:
        """
        Check if an app key is supported.
        
        Args:
            app_key: The app key to check
            
        Returns:
            True if the app is supported, False otherwise
        """
        return app_key in cls.get_supported_apps()
    
    @classmethod
    def reload_constants(cls) -> None:
        """Force reload of constants from file."""
        cls._server_urls = None
        cls._load_constants()

# Convenience functions for direct access
def get_composio_mcp_url(app_key: str, user_id: str) -> Optional[str]:
    """
    Convenience function to generate a user-specific Composio MCP URL.
    
    Args:
        app_key: The app key (e.g., "gmail", "slack", "github")
        user_id: The user's UUID
        
    Returns:
        The complete user-specific MCP URL or None if app_key not found
    """
    return ComposioMCPConstants.generate_user_mcp_url(app_key, user_id)

def get_supported_composio_apps() -> List[str]:
    """
    Convenience function to get list of supported Composio apps.
    
    Returns:
        List of supported app keys
    """
    return ComposioMCPConstants.get_supported_apps()

def is_composio_app_supported(app_key: str) -> bool:
    """
    Convenience function to check if an app is supported.
    
    Args:
        app_key: The app key to check
        
    Returns:
        True if the app is supported, False otherwise
    """
    return ComposioMCPConstants.is_app_supported(app_key)
