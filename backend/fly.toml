# fly.toml app configuration file generated for atlas-backend-api on 2025-05-31T17:48:04+05:30
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'atlas-backend-api'
primary_region = 'bom'

[build]

[processes]
  app = "gunicorn api:app --workers 8 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000 --timeout 1800 --graceful-timeout 600 --keep-alive 1800 --max-requests 0 --max-requests-jitter 0 --forwarded-allow-ips '*' --worker-connections 2000 --worker-tmp-dir /dev/shm --preload --log-level info --access-logfile - --error-logfile - --capture-output --enable-stdio-inheritance --threads 2"
  worker = "python -m dramatiq --processes 4 --threads 4 run_agent_background"

[http_service]
  internal_port = 8000
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '4gb'
  cpu_kind = 'shared'
  cpus = 4
  processes = ['app']

[[vm]]
  memory = '4gb'
  cpu_kind = 'shared'
  cpus = 4
  processes = ['worker']
