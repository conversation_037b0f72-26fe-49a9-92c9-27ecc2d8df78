#!/usr/bin/env python3
"""
Test Script for Composio MCP URL Generation Flow

This script tests the complete flow for generating Composio MCP URLs:
1. Generate session UUID
2. Call Composio API to install/register the session
3. Get MCP URL from status endpoint
4. Validate the URL format

Run this script to verify the flow works before implementing in the main codebase.
"""

import asyncio
import httpx
import uuid
import hashlib
import json
from typing import Optional, Dict, Any

class ComposioMCPTester:
    def __init__(self):
        self.composio_base_url = "https://mcp.composio.dev"
        self.timeout = 30.0
        
    def generate_session_uuid(self, user_id: str, app_key: str) -> str:
        """
        Generate a deterministic session UUID for user+app combination.
        This ensures the same user gets the same session for the same app.
        """
        # Create a deterministic UUID based on user_id and app_key
        combined = f"{user_id}:{app_key}"
        hash_object = hashlib.sha256(combined.encode())
        hash_hex = hash_object.hexdigest()
        
        # Convert to UUID format (8-4-4-4-12)
        session_uuid = f"{hash_hex[:8]}-{hash_hex[8:12]}-{hash_hex[12:16]}-{hash_hex[16:20]}-{hash_hex[20:32]}"
        return session_uuid
    
    async def test_install_app(self, app_key: str, session_uuid: str) -> Dict[str, Any]:
        """
        Test Step 1: Install/register the app with Composio API
        """
        url = f"{self.composio_base_url}/api/apps/{app_key}/install"
        
        payload = {"uuid": session_uuid, "framework": "mcp"}
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json, text/plain, */*",
            "Origin": "https://mcp.composio.dev",
            "Referer": f"https://mcp.composio.dev/{app_key}/{session_uuid}",
            "User-Agent": "Atlas-Test/1.0",
        }
        
        cookies = {
            "uuid": session_uuid,
            "isActiveUser": session_uuid,
        }
        
        print(f"🔄 Step 1: Installing app '{app_key}' with session '{session_uuid}'")
        print(f"   URL: {url}")
        print(f"   Payload: {payload}")
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    url, 
                    json=payload, 
                    headers=headers, 
                    cookies=cookies
                )
                
                print(f"   Status: {response.status_code}")
                print(f"   Response: {response.text}")
                
                return {
                    "success": response.status_code == 200,
                    "status_code": response.status_code,
                    "response": response.json() if response.status_code == 200 else response.text,
                    "headers": dict(response.headers)
                }
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            return {"success": False, "error": str(e)}
    
    async def test_get_status(self, app_key: str, session_uuid: str) -> Dict[str, Any]:
        """
        Test Step 2: Get the MCP URL from status endpoint
        """
        url = f"{self.composio_base_url}/api/apps/{app_key}"
        
        headers = {
            "Accept": "application/json, text/plain, */*",
            "Origin": "https://mcp.composio.dev",
            "Referer": f"https://mcp.composio.dev/{app_key}/{session_uuid}",
            "User-Agent": "Atlas-Test/1.0",
        }
        
        cookies = {
            "uuid": session_uuid,
            "isActiveUser": session_uuid,
        }
        
        params = {"uuid": session_uuid}
        
        print(f"🔄 Step 2: Getting status for app '{app_key}' with session '{session_uuid}'")
        print(f"   URL: {url}")
        print(f"   Params: {params}")
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    url,
                    params=params,
                    headers=headers,
                    cookies=cookies
                )
                
                print(f"   Status: {response.status_code}")
                print(f"   Response: {response.text}")
                
                if response.status_code == 200:
                    data = response.json()
                    mcp_url = data.get("sseUrl")
                    
                    return {
                        "success": True,
                        "status_code": response.status_code,
                        "response": data,
                        "mcp_url": mcp_url,
                        "headers": dict(response.headers)
                    }
                else:
                    return {
                        "success": False,
                        "status_code": response.status_code,
                        "response": response.text
                    }
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            return {"success": False, "error": str(e)}
    
    def validate_mcp_url(self, mcp_url: str, app_key: str, session_uuid: str) -> Dict[str, Any]:
        """
        Test Step 3: Validate the MCP URL format
        """
        print(f"🔄 Step 3: Validating MCP URL format")
        print(f"   URL: {mcp_url}")
        
        expected_pattern = f"https://mcp.composio.dev/partner/composio/{app_key}/mcp?customerId={session_uuid}"
        
        validation = {
            "url": mcp_url,
            "expected_pattern": expected_pattern,
            "matches_pattern": mcp_url == expected_pattern,
            "contains_app_key": app_key in mcp_url,
            "contains_session_uuid": session_uuid in mcp_url,
            "has_customer_id": "customerId=" in mcp_url,
            "is_mcp_endpoint": mcp_url.endswith("/mcp") or "/mcp?" in mcp_url
        }
        
        print(f"   Expected: {expected_pattern}")
        print(f"   Matches: {validation['matches_pattern']}")
        print(f"   Contains app_key: {validation['contains_app_key']}")
        print(f"   Contains session_uuid: {validation['contains_session_uuid']}")
        print(f"   Has customerId param: {validation['has_customer_id']}")
        print(f"   Is MCP endpoint: {validation['is_mcp_endpoint']}")
        
        return validation
    
    async def test_complete_flow(self, user_id: str, app_key: str) -> Dict[str, Any]:
        """
        Test the complete flow for a user and app
        """
        print(f"\n🚀 Testing Complete Composio MCP Flow")
        print(f"   User ID: {user_id}")
        print(f"   App Key: {app_key}")
        print("=" * 60)
        
        # Step 1: Generate session UUID
        session_uuid = self.generate_session_uuid(user_id, app_key)
        print(f"✅ Generated session UUID: {session_uuid}")
        
        # Step 2: Install app
        install_result = await self.test_install_app(app_key, session_uuid)
        if not install_result["success"]:
            print(f"❌ Install failed: {install_result}")
            return {"success": False, "step": "install", "result": install_result}
        
        print(f"✅ Install successful")
        
        # Step 3: Get status and MCP URL
        status_result = await self.test_get_status(app_key, session_uuid)
        if not status_result["success"]:
            print(f"❌ Status check failed: {status_result}")
            return {"success": False, "step": "status", "result": status_result}
        
        mcp_url = status_result.get("mcp_url")
        if not mcp_url:
            print(f"❌ No MCP URL in response: {status_result}")
            return {"success": False, "step": "mcp_url_extraction", "result": status_result}
        
        print(f"✅ Got MCP URL: {mcp_url}")
        
        # Step 4: Validate URL format
        validation = self.validate_mcp_url(mcp_url, app_key, session_uuid)
        
        if validation["matches_pattern"]:
            print(f"✅ URL format is correct!")
        else:
            print(f"⚠️  URL format differs from expected pattern")
        
        return {
            "success": True,
            "user_id": user_id,
            "app_key": app_key,
            "session_uuid": session_uuid,
            "mcp_url": mcp_url,
            "install_result": install_result,
            "status_result": status_result,
            "validation": validation
        }

async def main():
    """
    Main test function - modify these values to test different scenarios
    """
    tester = ComposioMCPTester()
    
    # Test cases - modify these as needed
    test_cases = [
        {"user_id": "test-user-123", "app_key": "gmail"},
        {"user_id": "test-user-456", "app_key": "slack"},
        {"user_id": "test-user-789", "app_key": "github"},
    ]
    
    print("🧪 Composio MCP Flow Tester")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 Test Case {i}/{len(test_cases)}")
        
        result = await tester.test_complete_flow(
            user_id=test_case["user_id"],
            app_key=test_case["app_key"]
        )
        
        if result["success"]:
            print(f"🎉 Test Case {i} PASSED")
            print(f"   MCP URL: {result['mcp_url']}")
        else:
            print(f"💥 Test Case {i} FAILED at step: {result.get('step', 'unknown')}")
        
        print("-" * 60)
    
    print("\n✨ Testing complete!")

if __name__ == "__main__":
    asyncio.run(main())
